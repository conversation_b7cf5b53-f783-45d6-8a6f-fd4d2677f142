import * as Yup from 'yup';

const REGEX_PATTERNS = {
    NAME: /^[a-zA-Z\s]+$/, // Only alphabets and spaces
    COORDINATE: /^-?\d+(\.\d+)?$/, // Validates latitude/longitude (e.g., -12.3456)
    PINCODE: /^\d{6}$/, // Exactly 6 digits
    ENCUMBRANCE_DOC: /^\d{1,6}\/\d{4}$/, // Format: docNo/year
    PROPERTY_TAX: /^([A-Z]{0,3}\d{1,3}|[A-Z]{2,5})[/-]\d{1,4}[/-]\d{4}(?:-[0-9]{2})?[/-]\d{3,6}$/i,
    SURVEY_NO: /^\d+[A-Z]?(?:\/\d+[A-Z]?)?$/i, // 123, 123/1, or 123/1A
    VILLAGE_DISTRICT: /^[A-Za-z\s.'-]+$/, // Letters, spaces, apostrophes, periods, hyphens
};

// Site Details Step Validation
export const siteDetailsSchema = Yup.object().shape({
    name: Yup.string()
        .trim()
        .required('Site name is required')
        .min(5, 'Site name must be at least 5 characters')
        .max(100, 'Site name must not exceed 100 characters')
        .matches(REGEX_PATTERNS.NAME, 'Site name must contain only letters and spaces'),
    
    addressLine1: Yup.string()
        .trim()
        .required('Address Line 1 is required')
        .max(200, 'Address Line 1 is too long'),
    
    addressLine2: Yup.string()
        .trim()
        .max(200, 'Address Line 2 is too long'),
    
    landmark: Yup.string()
        .trim()
        .max(100, 'Landmark is too long'),
    
    pincode: Yup.string()
        .trim()
        .required('Pincode is required')
        .matches(REGEX_PATTERNS.PINCODE, 'Pincode must be exactly 6 digits'),
    
    state: Yup.string()
        .trim()
        .required('State is required')
        .min(2, 'State name must be at least 2 characters')
        .max(100, 'State name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'State name contains invalid characters'),
    
    district: Yup.string()
        .trim()
        .required('District is required')
        .min(2, 'District name must be at least 2 characters')
        .max(100, 'District name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'District name contains invalid characters'),
    
    plotArea: Yup.number()
        .required('Plot area is required')
        .positive('Plot area must be a positive number')
        .min(0.1, 'Plot area must be at least 0.1 sq ft'),
    
    price: Yup.number()
        .required('Price is required')
        .positive('Price must be a positive number')
        .min(1, 'Price must be at least ₹1'),
    
    siteImages: Yup.array()
        .min(1, 'At least one site image is required')
        .max(10, 'Maximum 10 images allowed')
        .of(
            Yup.object().shape({
                uri: Yup.string().required(),
                name: Yup.string().required(),
                mimeType: Yup.string().required(),
            })
        ),
});

// Location Step Validation
export const locationSchema = Yup.object().shape({
    location: Yup.string()
        .trim()
        .required('Location is required')
        .max(200, 'Location is too long'),
    
    latitude: Yup.string()
        .required('Latitude is required')
        .matches(REGEX_PATTERNS.COORDINATE, 'Invalid latitude format')
        .test('latitude-range', 'Latitude must be between -90 and 90', function(value) {
            if (!value) return false;
            const lat = parseFloat(value);
            return lat >= -90 && lat <= 90;
        }),
    
    longitude: Yup.string()
        .required('Longitude is required')
        .matches(REGEX_PATTERNS.COORDINATE, 'Invalid longitude format')
        .test('longitude-range', 'Longitude must be between -180 and 180', function(value) {
            if (!value) return false;
            const lng = parseFloat(value);
            return lng >= -180 && lng <= 180;
        }),
});

// Encumbrance Step Validation
export const encumbranceSchema = Yup.object().shape({
    encOwnerName: Yup.string()
        .trim()
        .required('Owner name is required')
        .min(2, 'Owner name must be at least 2 characters')
        .max(100, 'Owner name must not exceed 100 characters')
        .matches(REGEX_PATTERNS.NAME, 'Owner name must contain only letters and spaces'),
    
    encDocumentNo: Yup.string()
        .trim()
        .required('Encumbrance Certificate number is required')
        .matches(REGEX_PATTERNS.ENCUMBRANCE_DOC, 'Format must be docNo/year (e.g., 1234/2024)')
        .test('valid-year', 'Year in EC number is invalid', function(value) {
            if (!value) return false;
            const [, yearPart] = value.split('/');
            const year = Number(yearPart);
            return year >= 1900 && year <= new Date().getFullYear();
        }),
    
    surveyNo: Yup.string()
        .trim()
        .required('Survey number is required')
        .min(1, 'Survey number is required')
        .max(20, 'Survey number is too long')
        .matches(REGEX_PATTERNS.SURVEY_NO, 'Survey number format: 123, 123/1, or 123/1A'),
    
    village: Yup.string()
        .trim()
        .min(2, 'Village name must be at least 2 characters')
        .max(100, 'Village name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Village name contains invalid characters'),
    
    subDistrict: Yup.string()
        .trim()
        .min(2, 'Sub-district name must be at least 2 characters')
        .max(100, 'Sub-district name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Sub-district name contains invalid characters'),
    
    District: Yup.string()
        .trim()
        .min(2, 'District name must be at least 2 characters')
        .max(100, 'District name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'District name contains invalid characters'),
    
    encumbranceCert: Yup.object()
        .required('Encumbrance Certificate is required')
        .shape({
            uri: Yup.string().required(),
            name: Yup.string().required(),
            mimeType: Yup.string().required(),
        }),
});

// Property Tax Step Validation
export const propertyTaxSchema = Yup.object().shape({
    ptrOwnerName: Yup.string()
        .trim()
        .required('Owner name is required')
        .min(2, 'Owner name must be at least 2 characters')
        .max(100, 'Owner name must not exceed 100 characters')
        .matches(REGEX_PATTERNS.NAME, 'Owner name must contain only letters and spaces'),
    
    ptrReciptNo: Yup.string()
        .trim()
        .required('Tax receipt number is required')
        .min(6, 'Tax receipt number must be at least 6 characters')
        .max(30, 'Tax receipt number must not exceed 30 characters')
        .matches(REGEX_PATTERNS.PROPERTY_TAX, 'Invalid tax receipt format (e.g., ZN/013/2024-25/00123)'),
    
    propertyTaxRec: Yup.object()
        .required('Property Tax Receipt is required')
        .shape({
            uri: Yup.string().required(),
            name: Yup.string().required(),
            mimeType: Yup.string().required(),
        }),
});

// Complete Site Validation Schema
export const completeSiteSchema = Yup.object().shape({
    ...siteDetailsSchema.fields,
    ...locationSchema.fields,
    ...encumbranceSchema.fields,
    ...propertyTaxSchema.fields,
});

// Initial Values
export const initialSiteValues = {
    name: '',
    addressLine1: '',
    addressLine2: '',
    landmark: '',
    location: '',
    pincode: '',
    state: '',
    district: '',
    plotArea: '',
    price: '',
    latitude: '',
    longitude: '',
    encOwnerName: '',
    encDocumentNo: '',
    surveyNo: '',
    village: '',
    subDistrict: '',
    District: '',
    ptrOwnerName: '',
    ptrReciptNo: '',
    siteImages: [],
    encumbranceCert: null,
    propertyTaxRec: null,
};

// Helper function to get validation schema for current step
export const getStepValidationSchema = (step) => {
    switch (step) {
        case 'siteDetails':
            return siteDetailsSchema;
        case 'location':
            return locationSchema;
        case 'encumbrance':
            return encumbranceSchema;
        case 'propertyTax':
            return propertyTaxSchema;
        case 'review':
            return completeSiteSchema;
        default:
            return Yup.object();
    }
};
